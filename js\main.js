// LustFlix - Main JavaScript

// DOM Content Loaded Event
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// Initialize Application
function initializeApp() {
    setupAgeVerification();
    setupMobileMenu();
    setupSearch();
    setupContentCards();
    setupScrollEffects();
    setupVideoPlayer();
    setupModals();
    setupDropdownMenus();
    generateDynamicContent();
}

// Age Verification System
function setupAgeVerification() {
    const ageModal = document.getElementById('ageModal');
    const confirmBtn = document.getElementById('confirmAge');
    const rejectBtn = document.getElementById('rejectAge');
    
    // Check if user has already verified age
    const ageVerified = localStorage.getItem('ageVerified');
    
    if (!ageVerified) {
        ageModal.style.display = 'flex';
    } else {
        ageModal.style.display = 'none';
    }
    
    confirmBtn.addEventListener('click', function() {
        localStorage.setItem('ageVerified', 'true');
        ageModal.style.display = 'none';
        document.body.style.overflow = 'auto';
    });
    
    rejectBtn.addEventListener('click', function() {
        window.location.href = 'https://www.google.com';
    });
}

// Mobile Menu Toggle
function setupMobileMenu() {
    const mobileToggle = document.querySelector('.mobile-menu-toggle');
    const mainNav = document.querySelector('.main-nav');
    
    if (mobileToggle && mainNav) {
        mobileToggle.addEventListener('click', function() {
            mainNav.classList.toggle('active');
            const icon = mobileToggle.querySelector('i');
            
            if (mainNav.classList.contains('active')) {
                icon.classList.remove('fa-bars');
                icon.classList.add('fa-times');
            } else {
                icon.classList.remove('fa-times');
                icon.classList.add('fa-bars');
            }
        });
        
        // Close menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!mobileToggle.contains(e.target) && !mainNav.contains(e.target)) {
                mainNav.classList.remove('active');
                const icon = mobileToggle.querySelector('i');
                icon.classList.remove('fa-times');
                icon.classList.add('fa-bars');
            }
        });
    }
}

// Search Functionality
function setupSearch() {
    const searchInput = document.querySelector('.search-container input');
    const searchBtn = document.querySelector('.search-btn');

    if (searchInput && searchBtn) {
        searchBtn.addEventListener('click', performSearch);
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });

        // Advanced search suggestions
        searchInput.addEventListener('input', function() {
            const query = this.value.toLowerCase();
            if (query.length > 1) {
                showSearchSuggestions(query);
            } else {
                hideSearchSuggestions();
            }
        });

        // Hide suggestions when clicking outside
        document.addEventListener('click', function(e) {
            if (!searchInput.contains(e.target)) {
                hideSearchSuggestions();
            }
        });
    }
}

function showSearchSuggestions(query) {
    let suggestions = [];

    // Search in categories
    Object.values(pornCategories).flat().forEach(category => {
        if (category.toLowerCase().includes(query)) {
            suggestions.push({
                type: 'category',
                text: category,
                icon: '🏷️'
            });
        }
    });

    // Search in regions
    Object.values(regions).forEach(region => {
        region.countries.forEach(country => {
            if (country.toLowerCase().includes(query)) {
                suggestions.push({
                    type: 'region',
                    text: country,
                    icon: region.flag
                });
            }
        });
    });

    // Popular search terms
    const popularTerms = ['amateur', 'milf', 'teen', 'anal', 'big tits', 'asian', 'latina', 'blonde', 'brunette', 'redhead'];
    popularTerms.forEach(term => {
        if (term.includes(query)) {
            suggestions.push({
                type: 'popular',
                text: term,
                icon: '🔥'
            });
        }
    });

    // Limit suggestions
    suggestions = suggestions.slice(0, 8);

    if (suggestions.length > 0) {
        displaySearchSuggestions(suggestions);
    } else {
        hideSearchSuggestions();
    }
}

function displaySearchSuggestions(suggestions) {
    let suggestionsContainer = document.querySelector('.search-suggestions');

    if (!suggestionsContainer) {
        suggestionsContainer = document.createElement('div');
        suggestionsContainer.className = 'search-suggestions';
        document.querySelector('.search-container').appendChild(suggestionsContainer);
    }

    suggestionsContainer.innerHTML = suggestions.map(suggestion => `
        <div class="suggestion-item" data-type="${suggestion.type}" data-text="${suggestion.text}">
            <span class="suggestion-icon">${suggestion.icon}</span>
            <span class="suggestion-text">${suggestion.text}</span>
            <span class="suggestion-type">${suggestion.type}</span>
        </div>
    `).join('');

    // Add click handlers
    suggestionsContainer.querySelectorAll('.suggestion-item').forEach(item => {
        item.addEventListener('click', function() {
            const text = this.dataset.text;
            document.querySelector('.search-container input').value = text;
            hideSearchSuggestions();
            performSearch();
        });
    });

    suggestionsContainer.style.display = 'block';
}

function hideSearchSuggestions() {
    const suggestionsContainer = document.querySelector('.search-suggestions');
    if (suggestionsContainer) {
        suggestionsContainer.style.display = 'none';
    }
}

function performSearch() {
    const searchInput = document.querySelector('.search-container input');
    const query = searchInput.value.trim();

    if (query) {
        hideSearchSuggestions();
        showSearchResults(query);
    }
}

function showSearchResults(query) {
    // Create search results modal
    const modal = createModal(`Search Results for "${query}"`, `
        <div class="search-results">
            <div class="search-filters">
                <div class="filter-tabs">
                    <button class="filter-tab active" data-filter="all">All Results</button>
                    <button class="filter-tab" data-filter="videos">Videos</button>
                    <button class="filter-tab" data-filter="categories">Categories</button>
                    <button class="filter-tab" data-filter="regions">Regions</button>
                    <button class="filter-tab" data-filter="models">Models</button>
                </div>
                <div class="region-filter">
                    <label>Region:</label>
                    <select id="regionSelect">
                        <option value="all">All Regions</option>
                        ${Object.entries(regions).map(([region, data]) =>
                            `<option value="${region}">${data.flag} ${region}</option>`
                        ).join('')}
                    </select>
                </div>
            </div>
            <div class="search-results-content" id="searchResultsContent">
                ${generateSearchResultsHTML(query)}
            </div>
        </div>
    `);

    // Setup filter functionality
    setupSearchFilters(query);
}

// Content Cards Interaction
function setupContentCards() {
    const contentCards = document.querySelectorAll('.content-card');
    
    contentCards.forEach(card => {
        const playBtn = card.querySelector('.play-btn');
        
        if (playBtn) {
            playBtn.addEventListener('click', function(e) {
                e.stopPropagation();
                const cardTitle = card.querySelector('h3').textContent;
                openVideoPlayer(cardTitle);
            });
        }
        
        card.addEventListener('click', function() {
            const cardTitle = this.querySelector('h3').textContent;
            showContentDetails(cardTitle);
        });
    });
}

// Scroll Effects
function setupScrollEffects() {
    const header = document.querySelector('header');
    
    window.addEventListener('scroll', function() {
        if (window.scrollY > 100) {
            header.style.background = 'rgba(10, 10, 10, 0.95)';
        } else {
            header.style.background = 'linear-gradient(180deg, rgba(10, 10, 10, 0.9), transparent)';
        }
    });
    
    // Intersection Observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);
    
    // Observe content sections
    const sections = document.querySelectorAll('.content-section, .categories-section, .premium-section');
    sections.forEach(section => {
        section.style.opacity = '0';
        section.style.transform = 'translateY(30px)';
        section.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(section);
    });
}

// Video Player
function setupVideoPlayer() {
    // This would integrate with a video player library like Video.js or Plyr
    console.log('Video player setup initialized');
}

function openVideoPlayer(title) {
    // Create modal for video player
    const modal = document.createElement('div');
    modal.className = 'video-modal';
    modal.innerHTML = `
        <div class="video-modal-content">
            <div class="video-header">
                <h3>${title}</h3>
                <button class="close-video">&times;</button>
            </div>
            <div class="video-container">
                <video controls autoplay>
                    <source src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4" type="video/mp4">
                    Your browser does not support the video tag.
                </video>
            </div>
            <div class="video-info">
                <div class="video-stats">
                    <span><i class="fas fa-eye"></i> 1.2M views</span>
                    <span><i class="fas fa-star"></i> 4.8 rating</span>
                    <span><i class="fas fa-clock"></i> 12:45</span>
                </div>
                <div class="video-actions">
                    <button class="action-btn"><i class="fas fa-thumbs-up"></i> Like</button>
                    <button class="action-btn"><i class="fas fa-heart"></i> Favorite</button>
                    <button class="action-btn"><i class="fas fa-share"></i> Share</button>
                    <button class="action-btn"><i class="fas fa-download"></i> Download</button>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    document.body.style.overflow = 'hidden';
    
    // Close modal functionality
    const closeBtn = modal.querySelector('.close-video');
    closeBtn.addEventListener('click', function() {
        document.body.removeChild(modal);
        document.body.style.overflow = 'auto';
    });
    
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            document.body.removeChild(modal);
            document.body.style.overflow = 'auto';
        }
    });
}

// Modal System
function setupModals() {
    // Login Modal
    const loginBtn = document.querySelector('.login-btn');
    if (loginBtn) {
        loginBtn.addEventListener('click', function(e) {
            e.preventDefault();
            openLoginModal();
        });
    }
    
    // Signup Modal
    const signupBtn = document.querySelector('.signup-btn');
    if (signupBtn) {
        signupBtn.addEventListener('click', function(e) {
            e.preventDefault();
            openSignupModal();
        });
    }
}

function openLoginModal() {
    const modal = createModal('Login to LustFlix', `
        <form class="auth-form">
            <div class="form-group">
                <label for="email">Email</label>
                <input type="email" id="email" required>
            </div>
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" required>
            </div>
            <div class="form-options">
                <label class="checkbox">
                    <input type="checkbox"> Remember me
                </label>
                <a href="#" class="forgot-password">Forgot password?</a>
            </div>
            <button type="submit" class="primary-btn">Sign In</button>
            <p class="auth-switch">Don't have an account? <a href="#" onclick="openSignupModal()">Sign up</a></p>
        </form>
    `);
}

function openSignupModal() {
    const modal = createModal('Join LustFlix', `
        <form class="auth-form" id="signupForm">
            <div class="form-group">
                <label for="username">Username</label>
                <input type="text" id="username" required minlength="3" maxlength="20">
                <span class="input-hint">3-20 characters, letters and numbers only</span>
            </div>
            <div class="form-group">
                <label for="email">Email</label>
                <input type="email" id="email" required>
                <span class="input-hint">We'll send a verification email</span>
            </div>
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" required minlength="8">
                <span class="input-hint">At least 8 characters with numbers and symbols</span>
            </div>
            <div class="form-group">
                <label for="confirmPassword">Confirm Password</label>
                <input type="password" id="confirmPassword" required>
                <span class="input-hint">Must match your password</span>
            </div>

            <!-- Human Verification Section -->
            <div class="human-verification">
                <h3>Human Verification</h3>
                <div class="captcha-container">
                    <div class="math-captcha">
                        <label>Solve this simple math problem:</label>
                        <div class="math-problem" id="mathProblem">
                            <span id="num1"></span> + <span id="num2"></span> =
                            <input type="number" id="mathAnswer" required placeholder="?">
                        </div>
                        <button type="button" class="refresh-captcha" onclick="generateMathCaptcha()">
                            <i class="fas fa-refresh"></i> New Problem
                        </button>
                    </div>

                    <div class="image-captcha">
                        <label>Select all images with adult content warnings:</label>
                        <div class="captcha-images" id="imageCaptcha">
                            <!-- Images will be generated dynamically -->
                        </div>
                        <button type="button" class="refresh-captcha" onclick="generateImageCaptcha()">
                            <i class="fas fa-refresh"></i> New Images
                        </button>
                    </div>

                    <div class="checkbox-captcha">
                        <label class="captcha-checkbox">
                            <input type="checkbox" id="humanCheck" required>
                            <span class="checkmark"></span>
                            I'm not a robot
                        </label>
                    </div>
                </div>
            </div>

            <div class="form-options">
                <label class="checkbox">
                    <input type="checkbox" id="termsCheck" required> I agree to the <a href="#" target="_blank">Terms of Service</a> and <a href="#" target="_blank">Privacy Policy</a>
                </label>
                <label class="checkbox">
                    <input type="checkbox" id="ageCheck" required> I confirm that I am 18 years or older and legally allowed to view adult content
                </label>
                <label class="checkbox">
                    <input type="checkbox" id="marketingCheck"> I want to receive updates and promotional emails (optional)
                </label>
            </div>

            <button type="submit" class="primary-btn" id="createAccountBtn" disabled>Create Account</button>
            <p class="auth-switch">Already have an account? <a href="#" onclick="openLoginModal()">Sign in</a></p>
        </form>
    `);

    // Initialize captcha and form validation
    setTimeout(() => {
        generateMathCaptcha();
        generateImageCaptcha();
        setupSignupValidation();
    }, 100);
}

function createModal(title, content) {
    const modal = document.createElement('div');
    modal.className = 'auth-modal';
    modal.innerHTML = `
        <div class="auth-modal-content">
            <div class="modal-header">
                <h2>${title}</h2>
                <button class="close-modal">&times;</button>
            </div>
            <div class="modal-body">
                ${content}
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    document.body.style.overflow = 'hidden';
    
    // Close modal functionality
    const closeBtn = modal.querySelector('.close-modal');
    closeBtn.addEventListener('click', function() {
        document.body.removeChild(modal);
        document.body.style.overflow = 'auto';
    });
    
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            document.body.removeChild(modal);
            document.body.style.overflow = 'auto';
        }
    });
    
    return modal;
}

// Generate Dynamic Content
function generateDynamicContent() {
    generateContentCards();
    generateCategories();
}

function generateContentCards() {
    const carousel = document.querySelector('.content-carousel');
    if (!carousel) return;
    
    // Clear existing placeholder
    carousel.innerHTML = '';
    
    const sampleContent = [
        {
            title: 'Premium HD Content',
            views: '2.1M',
            rating: '4.9',
            duration: '15:32',
            quality: '4K',
            region: '🇺🇸',
            tags: ['premium', 'hd', 'amateur'],
            previewUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4'
        },
        {
            title: 'Asian Beauty Collection',
            views: '1.8M',
            rating: '4.7',
            duration: '22:15',
            quality: 'HD',
            region: '🇯🇵',
            tags: ['asian', 'japanese', 'beauty'],
            previewUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4'
        },
        {
            title: 'European Compilation',
            views: '950K',
            rating: '4.6',
            duration: '8:45',
            quality: 'HD',
            region: '🇩🇪',
            tags: ['european', 'compilation', 'german'],
            previewUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4'
        },
        {
            title: 'Latina Live Stream',
            views: '3.2M',
            rating: '4.8',
            duration: '18:20',
            quality: 'HD',
            region: '🇧🇷',
            tags: ['latina', 'live', 'brazilian'],
            previewUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerEscapes.mp4'
        },
        {
            title: 'French Premium Series',
            views: '1.5M',
            rating: '4.9',
            duration: '25:10',
            quality: '4K',
            region: '🇫🇷',
            tags: ['french', 'premium', 'series'],
            previewUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerFun.mp4'
        },
        {
            title: 'Interactive VR Experience',
            views: '2.7M',
            rating: '4.8',
            duration: '12:30',
            quality: 'VR',
            region: '🇬🇧',
            tags: ['interactive', 'vr', 'british'],
            previewUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerJoyrides.mp4'
        }
    ];
    
    sampleContent.forEach(content => {
        const card = createContentCard(content);
        carousel.appendChild(card);
    });
    
    // Re-setup event listeners for new cards
    setupContentCards();
}

// Search Results and Filters
function generateSearchResultsHTML(query) {
    const sampleResults = [
        { title: `${query} HD Collection`, views: '2.1M', rating: '4.9', duration: '15:32', quality: '4K', region: '🇺🇸', tags: [query, 'hd', 'premium'] },
        { title: `Best ${query} Videos`, views: '1.8M', rating: '4.7', duration: '22:15', quality: 'HD', region: '🇯🇵', tags: [query, 'asian', 'popular'] },
        { title: `${query} Compilation`, views: '950K', rating: '4.6', duration: '8:45', quality: 'HD', region: '🇩🇪', tags: [query, 'compilation', 'european'] },
        { title: `Live ${query} Stream`, views: '3.2M', rating: '4.8', duration: '18:20', quality: 'HD', region: '🇧🇷', tags: [query, 'live', 'latina'] },
        { title: `${query} Premium`, views: '1.5M', rating: '4.9', duration: '25:10', quality: '4K', region: '🇫🇷', tags: [query, 'premium', 'french'] },
        { title: `Interactive ${query}`, views: '2.7M', rating: '4.8', duration: '12:30', quality: 'HD', region: '🇬🇧', tags: [query, 'interactive', 'british'] }
    ];

    return `
        <div class="results-grid">
            ${sampleResults.map(result => `
                <div class="result-card content-card">
                    <div class="card-image">
                        <img src="https://via.placeholder.com/300x169/e91e63/ffffff?text=${encodeURIComponent(result.title)}" alt="${result.title}">
                        <span class="duration">${result.duration}</span>
                        <div class="card-overlay">
                            <button class="play-btn"><i class="fas fa-play"></i></button>
                            <button class="preview-btn" title="Preview"><i class="fas fa-eye"></i></button>
                            <button class="favorite-btn" title="Add to Favorites"><i class="far fa-heart"></i></button>
                        </div>
                    </div>
                    <div class="card-info">
                        <h3>${result.title}</h3>
                        <div class="meta-info">
                            <span class="views"><i class="fas fa-eye"></i> ${result.views}</span>
                            <span class="rating"><i class="fas fa-star"></i> ${result.rating}</span>
                            <span class="quality">${result.quality}</span>
                            <span class="region">${result.region}</span>
                        </div>
                        <div class="card-tags">
                            ${result.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                        </div>
                    </div>
                </div>
            `).join('')}
        </div>
    `;
}

function setupSearchFilters(query) {
    const filterTabs = document.querySelectorAll('.filter-tab');
    const regionSelect = document.getElementById('regionSelect');
    const resultsContent = document.getElementById('searchResultsContent');

    // Filter tabs
    filterTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            filterTabs.forEach(t => t.classList.remove('active'));
            this.classList.add('active');

            const filter = this.dataset.filter;
            updateSearchResults(query, filter, regionSelect.value);
        });
    });

    // Region filter
    if (regionSelect) {
        regionSelect.addEventListener('change', function() {
            const activeFilter = document.querySelector('.filter-tab.active').dataset.filter;
            updateSearchResults(query, activeFilter, this.value);
        });
    }
}

function updateSearchResults(query, filter, region) {
    const resultsContent = document.getElementById('searchResultsContent');

    // This would normally fetch filtered results from server
    let filteredHTML = generateSearchResultsHTML(query);

    if (filter === 'categories') {
        filteredHTML = generateCategoryResults(query);
    } else if (filter === 'regions') {
        filteredHTML = generateRegionResults(query);
    }

    resultsContent.innerHTML = filteredHTML;

    // Re-setup event listeners for new cards
    setupContentCards();
}

function generateCategoryResults(query) {
    const matchingCategories = [];
    Object.entries(pornCategories).forEach(([letter, categories]) => {
        categories.forEach(category => {
            if (category.toLowerCase().includes(query.toLowerCase())) {
                matchingCategories.push(category);
            }
        });
    });

    return `
        <div class="category-results">
            <h3>Matching Categories</h3>
            <div class="categories-grid">
                ${matchingCategories.map(category => `
                    <div class="category-card" data-category="${category}">
                        <img src="https://via.placeholder.com/200x120/e91e63/ffffff?text=${encodeURIComponent(category)}" alt="${category}">
                        <h4>${category}</h4>
                        <span class="video-count">${Math.floor(Math.random() * 5000) + 100} videos</span>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
}

function generateRegionResults(query) {
    const matchingRegions = [];
    Object.entries(regions).forEach(([regionName, regionData]) => {
        if (regionName.toLowerCase().includes(query.toLowerCase())) {
            matchingRegions.push({ name: regionName, ...regionData });
        }
        regionData.countries.forEach(country => {
            if (country.toLowerCase().includes(query.toLowerCase())) {
                matchingRegions.push({ name: country, flag: regionData.flag, isCountry: true });
            }
        });
    });

    return `
        <div class="region-results">
            <h3>Matching Regions</h3>
            <div class="regions-grid">
                ${matchingRegions.map(region => `
                    <div class="region-card" data-region="${region.name}">
                        <div class="region-flag">${region.flag}</div>
                        <h4>${region.name}</h4>
                        <span class="video-count">${Math.floor(Math.random() * 2000) + 50} videos</span>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
}

// Category Toggle Functionality
function setupCategoryToggles() {
    const toggleButtons = document.querySelectorAll('.toggle-letter');

    toggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            const letter = this.dataset.letter;
            const categoriesContainer = document.getElementById(`categories-${letter}`);
            const icon = this.querySelector('i');

            if (categoriesContainer.style.display === 'none' || !categoriesContainer.style.display) {
                categoriesContainer.style.display = 'grid';
                icon.classList.remove('fa-chevron-down');
                icon.classList.add('fa-chevron-up');
                this.classList.add('active');
            } else {
                categoriesContainer.style.display = 'none';
                icon.classList.remove('fa-chevron-up');
                icon.classList.add('fa-chevron-down');
                this.classList.remove('active');
            }
        });
    });

    // Initially show first few letters
    ['A', 'B', 'C'].forEach(letter => {
        const button = document.querySelector(`[data-letter="${letter}"]`);
        if (button) {
            button.click();
        }
    });
}

function createContentCard(content) {
    const card = document.createElement('div');
    card.className = 'content-card';
    card.innerHTML = `
        <div class="card-image" data-preview-url="${content.previewUrl || ''}">
            <img src="https://via.placeholder.com/300x169/e91e63/ffffff?text=18+" alt="${content.title}">
            <span class="duration">${content.duration}</span>
            <div class="card-overlay">
                <button class="play-btn"><i class="fas fa-play"></i></button>
                <button class="preview-btn" title="Preview"><i class="fas fa-eye"></i></button>
                <button class="favorite-btn" title="Add to Favorites"><i class="fas fa-heart"></i></button>
            </div>
            <div class="video-preview" style="display: none;">
                <video muted loop>
                    <source src="${content.previewUrl || 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4'}" type="video/mp4">
                </video>
            </div>
        </div>
        <div class="card-info">
            <h3>${content.title}</h3>
            <div class="meta-info">
                <span class="views"><i class="fas fa-eye"></i> ${content.views}</span>
                <span class="rating"><i class="fas fa-star"></i> ${content.rating}</span>
                <span class="quality">${content.quality || 'HD'}</span>
                <span class="region">${content.region || '🇺🇸'}</span>
            </div>
            <div class="card-tags">
                ${(content.tags || ['amateur', 'hd']).map(tag => `<span class="tag">${tag}</span>`).join('')}
            </div>
        </div>
    `;

    // Add hover preview functionality
    setupVideoPreview(card);

    return card;
}

// Video Preview Functionality
function setupVideoPreview(card) {
    const cardImage = card.querySelector('.card-image');
    const previewVideo = card.querySelector('.video-preview video');
    const previewContainer = card.querySelector('.video-preview');
    const staticImage = card.querySelector('.card-image img');
    const previewBtn = card.querySelector('.preview-btn');
    const favoriteBtn = card.querySelector('.favorite-btn');

    let previewTimeout;
    let isPreviewActive = false;

    // Hover to start preview
    cardImage.addEventListener('mouseenter', function() {
        if (!isPreviewActive) {
            previewTimeout = setTimeout(() => {
                startVideoPreview();
            }, 1000); // Start preview after 1 second hover
        }
    });

    // Leave to stop preview
    cardImage.addEventListener('mouseleave', function() {
        clearTimeout(previewTimeout);
        if (isPreviewActive) {
            stopVideoPreview();
        }
    });

    // Click preview button
    if (previewBtn) {
        previewBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            if (isPreviewActive) {
                stopVideoPreview();
            } else {
                startVideoPreview();
            }
        });
    }

    // Favorite button
    if (favoriteBtn) {
        favoriteBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            toggleFavorite(this);
        });
    }

    function startVideoPreview() {
        if (previewVideo && previewContainer) {
            staticImage.style.opacity = '0';
            previewContainer.style.display = 'block';
            previewVideo.currentTime = 0;
            previewVideo.play().catch(console.error);
            isPreviewActive = true;

            // Update preview button icon
            if (previewBtn) {
                previewBtn.innerHTML = '<i class="fas fa-pause"></i>';
                previewBtn.title = 'Stop Preview';
            }
        }
    }

    function stopVideoPreview() {
        if (previewVideo && previewContainer) {
            previewVideo.pause();
            previewContainer.style.display = 'none';
            staticImage.style.opacity = '1';
            isPreviewActive = false;

            // Update preview button icon
            if (previewBtn) {
                previewBtn.innerHTML = '<i class="fas fa-eye"></i>';
                previewBtn.title = 'Preview';
            }
        }
    }
}

function toggleFavorite(button) {
    const icon = button.querySelector('i');
    const isFavorited = icon.classList.contains('fas');

    if (isFavorited) {
        icon.classList.remove('fas');
        icon.classList.add('far');
        button.title = 'Add to Favorites';
        button.style.color = '';
    } else {
        icon.classList.remove('far');
        icon.classList.add('fas');
        button.title = 'Remove from Favorites';
        button.style.color = '#e91e63';
    }

    // Add animation
    button.style.transform = 'scale(1.2)';
    setTimeout(() => {
        button.style.transform = '';
    }, 200);
}

// Comprehensive A-Z Categories Data
const pornCategories = {
    'A': ['Amateur', 'Anal', 'Asian', 'Arab', 'African', 'Australian', 'Austrian', 'American', 'Anime', 'Ass'],
    'B': ['BBW', 'BDSM', 'Big Tits', 'Big Ass', 'Blonde', 'Brunette', 'British', 'Brazilian', 'Bisexual', 'Bondage'],
    'C': ['Creampie', 'Cumshot', 'Couple', 'Cuckold', 'Compilation', 'Cosplay', 'Cam', 'Chinese', 'Czech', 'Canadian'],
    'D': ['Deepthroat', 'Double Penetration', 'Dildo', 'Doggystyle', 'Danish', 'Dutch', 'Domination', 'Dirty Talk', 'Desi', 'Daddy'],
    'E': ['Ebony', 'European', 'Exhibitionist', 'Extreme', 'English', 'Egyptian', 'Ethiopian', 'Erotic', 'Escort', 'Emo'],
    'F': ['Fetish', 'Facial', 'Fingering', 'Fisting', 'French', 'Finnish', 'Femdom', 'Foursome', 'Feet', 'Fantasy'],
    'G': ['Group', 'German', 'Greek', 'Gangbang', 'Gay', 'Granny', 'Goth', 'Girlfriend', 'Gloryhole', 'Gaping'],
    'H': ['Hardcore', 'Handjob', 'Hairy', 'Hungarian', 'Hentai', 'Homemade', 'Huge Tits', 'Hispanic', 'Hotwife', 'Humiliation'],
    'I': ['Indian', 'Italian', 'Interracial', 'Irish', 'Israeli', 'Indonesian', 'Interactive', 'Innocent', 'Incest', 'Insertion'],
    'J': ['Japanese', 'Jamaican', 'Jerk Off', 'Juicy', 'Jocks', 'Jerking', 'Joi', 'Jav', 'Jungle', 'Jizz'],
    'K': ['Korean', 'Kinky', 'Kitchen', 'Kissing', 'Kink', 'Kamasutra', 'Knockout', 'Knee High', 'Kegel', 'Kidnap'],
    'L': ['Latina', 'Lesbian', 'Live', 'Lingerie', 'Licking', 'Lebanese', 'Lithuanian', 'Lactating', 'Lapdance', 'Luxury'],
    'M': ['MILF', 'Masturbation', 'Massage', 'Mexican', 'Malaysian', 'Moroccan', 'Missionary', 'Mature', 'Muscle', 'Maid'],
    'N': ['Norwegian', 'Nude', 'Nipples', 'Natural', 'Naughty', 'Nurse', 'Nylon', 'Nudist', 'Neighbor', 'Nasty'],
    'O': ['Oral', 'Orgasm', 'Outdoor', 'Office', 'Orgy', 'Old', 'Oil', 'Onlyfans', 'Oriental', 'Orgies'],
    'P': ['POV', 'Public', 'Pornstar', 'Polish', 'Pakistani', 'Portuguese', 'Petite', 'Pregnant', 'Party', 'Pussy'],
    'Q': ['Quickie', 'Queen', 'Quarantine', 'Queer', 'Quality', 'Quiet', 'Quiver', 'Quest', 'Quirky', 'Quota'],
    'R': ['Redhead', 'Russian', 'Rough', 'Reality', 'Romanian', 'Roleplay', 'Rimming', 'Riding', 'Romantic', 'Raw'],
    'S': ['Solo', 'Squirting', 'Spanish', 'Swedish', 'Swiss', 'Swinger', 'Smoking', 'Schoolgirl', 'Shemale', 'Strapon'],
    'T': ['Teen', 'Threesome', 'Thai', 'Turkish', 'Taiwanese', 'Toys', 'Taboo', 'Titjob', 'Tattoo', 'Tight'],
    'U': ['Ukrainian', 'Uniform', 'Upskirt', 'Uncut', 'Underwater', 'University', 'Ugly', 'Ultimate', 'Uncensored', 'Unique'],
    'V': ['Vintage', 'Vietnamese', 'Venezuelan', 'Virtual', 'Voyeur', 'Vibrator', 'Virgin', 'Verified', 'Vacation', 'Vixen'],
    'W': ['Webcam', 'Wife', 'Wet', 'White', 'Wrestling', 'Worship', 'Wild', 'Whore', 'Wax', 'Workout'],
    'X': ['XXX', 'Xmas', 'Xtreme', 'X-rated', 'Xenophilia', 'Xerotic', 'Xvideos', 'Xhamster', 'Xxx Parody', 'X-art'],
    'Y': ['Young', 'Yoga', 'Yummy', 'Yacht', 'Yard', 'Yearning', 'Yelling', 'Yielding', 'Yoni', 'Youth'],
    'Z': ['Zombie', 'Zone', 'Zulu', 'Zesty', 'Zen', 'Zap', 'Zealous', 'Zigzag', 'Zip', 'Zoom']
};

// Regions with flags
const regions = {
    'North America': {
        flag: '🇺🇸',
        countries: ['USA', 'Canada', 'Mexico'],
        codes: ['US', 'CA', 'MX']
    },
    'Europe': {
        flag: '🇪🇺',
        countries: ['Germany', 'France', 'UK', 'Italy', 'Spain', 'Netherlands', 'Poland', 'Czech Republic', 'Russia', 'Ukraine'],
        codes: ['DE', 'FR', 'GB', 'IT', 'ES', 'NL', 'PL', 'CZ', 'RU', 'UA']
    },
    'Asia': {
        flag: '🇯🇵',
        countries: ['Japan', 'China', 'Korea', 'Thailand', 'India', 'Philippines', 'Indonesia', 'Vietnam', 'Malaysia', 'Singapore'],
        codes: ['JP', 'CN', 'KR', 'TH', 'IN', 'PH', 'ID', 'VN', 'MY', 'SG']
    },
    'Latin America': {
        flag: '🇧🇷',
        countries: ['Brazil', 'Argentina', 'Colombia', 'Venezuela', 'Chile', 'Peru', 'Ecuador', 'Uruguay', 'Paraguay', 'Bolivia'],
        codes: ['BR', 'AR', 'CO', 'VE', 'CL', 'PE', 'EC', 'UY', 'PY', 'BO']
    },
    'Middle East': {
        flag: '🇸🇦',
        countries: ['Saudi Arabia', 'UAE', 'Turkey', 'Iran', 'Israel', 'Lebanon', 'Jordan', 'Kuwait', 'Qatar', 'Bahrain'],
        codes: ['SA', 'AE', 'TR', 'IR', 'IL', 'LB', 'JO', 'KW', 'QA', 'BH']
    },
    'Africa': {
        flag: '🇿🇦',
        countries: ['South Africa', 'Nigeria', 'Egypt', 'Kenya', 'Morocco', 'Ghana', 'Ethiopia', 'Uganda', 'Tanzania', 'Algeria'],
        codes: ['ZA', 'NG', 'EG', 'KE', 'MA', 'GH', 'ET', 'UG', 'TZ', 'DZ']
    },
    'Oceania': {
        flag: '🇦🇺',
        countries: ['Australia', 'New Zealand', 'Fiji', 'Papua New Guinea', 'Samoa', 'Tonga', 'Vanuatu', 'Solomon Islands'],
        codes: ['AU', 'NZ', 'FJ', 'PG', 'WS', 'TO', 'VU', 'SB']
    }
};

function generateCategories() {
    const categoriesGrid = document.querySelector('.categories-grid');
    if (!categoriesGrid) return;

    // Clear existing placeholder
    categoriesGrid.innerHTML = '';

    // Create alphabetical category sections
    Object.keys(pornCategories).forEach(letter => {
        const letterSection = document.createElement('div');
        letterSection.className = 'letter-section';
        letterSection.innerHTML = `
            <div class="letter-header">
                <h3>${letter}</h3>
                <button class="toggle-letter" data-letter="${letter}">
                    <i class="fas fa-chevron-down"></i>
                </button>
            </div>
            <div class="letter-categories" id="categories-${letter}">
                ${pornCategories[letter].map(category => `
                    <div class="category-card" data-category="${category}">
                        <img src="https://via.placeholder.com/200x120/e91e63/ffffff?text=${encodeURIComponent(category)}" alt="${category}">
                        <h4>${category}</h4>
                        <span class="video-count">${Math.floor(Math.random() * 5000) + 100} videos</span>
                    </div>
                `).join('')}
            </div>
        `;
        categoriesGrid.appendChild(letterSection);
    });

    // Add toggle functionality
    setupCategoryToggles();
}

function showContentDetails(title) {
    console.log('Showing details for:', title);
    // Here you would implement content details page/modal
}

// Utility Functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Captcha and Validation Functions
function generateMathCaptcha() {
    const num1 = Math.floor(Math.random() * 20) + 1;
    const num2 = Math.floor(Math.random() * 20) + 1;

    document.getElementById('num1').textContent = num1;
    document.getElementById('num2').textContent = num2;
    document.getElementById('mathAnswer').value = '';

    // Store correct answer for validation
    window.correctMathAnswer = num1 + num2;
}

function generateImageCaptcha() {
    const imageCaptcha = document.getElementById('imageCaptcha');
    if (!imageCaptcha) return;

    const images = [
        { src: 'https://via.placeholder.com/80x80/ff6b6b/ffffff?text=18+', isAdult: true, id: 1 },
        { src: 'https://via.placeholder.com/80x80/4ecdc4/ffffff?text=SFW', isAdult: false, id: 2 },
        { src: 'https://via.placeholder.com/80x80/45b7d1/ffffff?text=SAFE', isAdult: false, id: 3 },
        { src: 'https://via.placeholder.com/80x80/f9ca24/ffffff?text=WARN', isAdult: true, id: 4 },
        { src: 'https://via.placeholder.com/80x80/6c5ce7/ffffff?text=OK', isAdult: false, id: 5 },
        { src: 'https://via.placeholder.com/80x80/e17055/ffffff?text=18+', isAdult: true, id: 6 }
    ];

    // Shuffle images
    const shuffledImages = images.sort(() => Math.random() - 0.5);

    imageCaptcha.innerHTML = '';
    window.correctImageIds = [];

    shuffledImages.forEach(image => {
        const imageDiv = document.createElement('div');
        imageDiv.className = 'captcha-image';
        imageDiv.innerHTML = `
            <img src="${image.src}" alt="Captcha Image" data-id="${image.id}">
            <input type="checkbox" class="image-checkbox" data-id="${image.id}">
        `;

        if (image.isAdult) {
            window.correctImageIds.push(image.id);
        }

        imageCaptcha.appendChild(imageDiv);
    });
}

function setupSignupValidation() {
    const form = document.getElementById('signupForm');
    const createBtn = document.getElementById('createAccountBtn');
    const inputs = form.querySelectorAll('input[required]');

    // Real-time validation
    inputs.forEach(input => {
        input.addEventListener('input', validateSignupForm);
        input.addEventListener('change', validateSignupForm);
    });

    // Form submission
    form.addEventListener('submit', handleSignupSubmit);

    // Initial validation
    validateSignupForm();
}

function validateSignupForm() {
    const username = document.getElementById('username').value;
    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    const mathAnswer = parseInt(document.getElementById('mathAnswer').value);
    const humanCheck = document.getElementById('humanCheck').checked;
    const termsCheck = document.getElementById('termsCheck').checked;
    const ageCheck = document.getElementById('ageCheck').checked;
    const createBtn = document.getElementById('createAccountBtn');

    let isValid = true;
    let errors = [];

    // Username validation
    if (username.length < 3 || username.length > 20) {
        isValid = false;
        errors.push('Username must be 3-20 characters');
    }

    if (!/^[a-zA-Z0-9]+$/.test(username)) {
        isValid = false;
        errors.push('Username can only contain letters and numbers');
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        isValid = false;
        errors.push('Please enter a valid email address');
    }

    // Password validation
    if (password.length < 8) {
        isValid = false;
        errors.push('Password must be at least 8 characters');
    }

    if (!/(?=.*[0-9])(?=.*[!@#$%^&*])/.test(password)) {
        isValid = false;
        errors.push('Password must contain at least one number and one symbol');
    }

    // Confirm password validation
    if (password !== confirmPassword) {
        isValid = false;
        errors.push('Passwords do not match');
    }

    // Math captcha validation
    if (mathAnswer !== window.correctMathAnswer) {
        isValid = false;
        errors.push('Please solve the math problem correctly');
    }

    // Image captcha validation
    const selectedImages = Array.from(document.querySelectorAll('.image-checkbox:checked'))
        .map(cb => parseInt(cb.dataset.id));

    const correctSelection = window.correctImageIds.sort().toString() === selectedImages.sort().toString();
    if (!correctSelection) {
        isValid = false;
        errors.push('Please select all images with adult content warnings');
    }

    // Checkbox validations
    if (!humanCheck) {
        isValid = false;
        errors.push('Please confirm you are not a robot');
    }

    if (!termsCheck) {
        isValid = false;
        errors.push('Please agree to the Terms of Service and Privacy Policy');
    }

    if (!ageCheck) {
        isValid = false;
        errors.push('You must be 18 or older to create an account');
    }

    // Update button state
    createBtn.disabled = !isValid;
    createBtn.textContent = isValid ? 'Create Account' : 'Please complete all fields';

    // Show/hide errors
    updateValidationErrors(errors);

    return isValid;
}

function updateValidationErrors(errors) {
    let errorContainer = document.querySelector('.validation-errors');

    if (errors.length > 0) {
        if (!errorContainer) {
            errorContainer = document.createElement('div');
            errorContainer.className = 'validation-errors';
            const form = document.getElementById('signupForm');
            form.insertBefore(errorContainer, form.querySelector('.form-options'));
        }

        errorContainer.innerHTML = `
            <h4>Please fix the following issues:</h4>
            <ul>
                ${errors.map(error => `<li>${error}</li>`).join('')}
            </ul>
        `;
    } else if (errorContainer) {
        errorContainer.remove();
    }
}

function handleSignupSubmit(e) {
    e.preventDefault();

    if (!validateSignupForm()) {
        return;
    }

    const createBtn = document.getElementById('createAccountBtn');
    const originalText = createBtn.textContent;

    // Show loading state
    createBtn.disabled = true;
    createBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating Account...';

    // Simulate account creation process
    setTimeout(() => {
        // In a real application, you would send the data to your server
        const formData = {
            username: document.getElementById('username').value,
            email: document.getElementById('email').value,
            password: document.getElementById('password').value,
            marketing: document.getElementById('marketingCheck').checked
        };

        console.log('Account creation data:', formData);

        // Show success message
        showSignupSuccess();

    }, 2000);
}

function showSignupSuccess() {
    const modal = document.querySelector('.auth-modal');
    const modalBody = modal.querySelector('.modal-body');

    modalBody.innerHTML = `
        <div class="signup-success">
            <div class="success-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <h3>Account Created Successfully!</h3>
            <p>Welcome to LustFlix! We've sent a verification email to your address.</p>
            <p>Please check your email and click the verification link to activate your account.</p>
            <div class="success-actions">
                <button class="primary-btn" onclick="closeModal()">Continue</button>
                <button class="secondary-btn" onclick="openLoginModal()">Sign In Now</button>
            </div>
        </div>
    `;
}

function closeModal() {
    const modal = document.querySelector('.auth-modal') || document.querySelector('.video-modal');
    if (modal) {
        document.body.removeChild(modal);
        document.body.style.overflow = 'auto';
    }
}

// Dropdown Menu Setup
function setupDropdownMenus() {
    setupCategoriesDropdown();
    setupRegionsDropdown();
}

function setupCategoriesDropdown() {
    const categoriesContainer = document.getElementById('dropdown-categories');
    const letterLinks = document.querySelectorAll('.letter-link');

    if (!categoriesContainer) return;

    // Show popular categories by default
    showDropdownCategories('popular');

    letterLinks.forEach(link => {
        link.addEventListener('click', function() {
            letterLinks.forEach(l => l.classList.remove('active'));
            this.classList.add('active');
            showDropdownCategories(this.dataset.letter);
        });
    });
}

function showDropdownCategories(letter) {
    const categoriesContainer = document.getElementById('dropdown-categories');
    if (!categoriesContainer) return;

    let categories = [];

    if (letter === 'popular') {
        categories = ['Amateur', 'MILF', 'Teen', 'Anal', 'Big Tits', 'Asian', 'Latina', 'Blonde', 'Brunette', 'Redhead', 'Lesbian', 'Threesome'];
    } else if (pornCategories[letter]) {
        categories = pornCategories[letter];
    }

    categoriesContainer.innerHTML = categories.map(category => `
        <div class="dropdown-category" data-category="${category}">
            ${category}
        </div>
    `).join('');

    // Add click handlers
    categoriesContainer.querySelectorAll('.dropdown-category').forEach(item => {
        item.addEventListener('click', function() {
            const category = this.dataset.category;
            document.querySelector('.search-container input').value = category;
            performSearch();
            // Close dropdown
            document.querySelector('.dropdown').classList.remove('active');
        });
    });
}

function setupRegionsDropdown() {
    const regionsContainer = document.querySelector('.regions-container');
    if (!regionsContainer) return;

    regionsContainer.innerHTML = Object.entries(regions).map(([regionName, regionData]) => `
        <div class="dropdown-region" data-region="${regionName}">
            <div class="region-flag">${regionData.flag}</div>
            <div class="region-name">${regionName}</div>
            <div class="region-count">${Math.floor(Math.random() * 1000) + 100} videos</div>
        </div>
    `).join('');

    // Add click handlers
    regionsContainer.querySelectorAll('.dropdown-region').forEach(item => {
        item.addEventListener('click', function() {
            const region = this.dataset.region;
            document.querySelector('.search-container input').value = region;
            performSearch();
            // Close dropdown
            document.querySelector('.dropdown').classList.remove('active');
        });
    });
}

// Export functions for global access
window.openLoginModal = openLoginModal;
window.openSignupModal = openSignupModal;
window.generateMathCaptcha = generateMathCaptcha;
window.generateImageCaptcha = generateImageCaptcha;
window.closeModal = closeModal;
