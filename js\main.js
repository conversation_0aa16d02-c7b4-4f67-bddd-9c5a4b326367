// LustFlix - Main JavaScript

// DOM Content Loaded Event
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// Initialize Application
function initializeApp() {
    setupAgeVerification();
    setupMobileMenu();
    setupSearch();
    setupContentCards();
    setupScrollEffects();
    setupVideoPlayer();
    setupModals();
    generateDynamicContent();
}

// Age Verification System
function setupAgeVerification() {
    const ageModal = document.getElementById('ageModal');
    const confirmBtn = document.getElementById('confirmAge');
    const rejectBtn = document.getElementById('rejectAge');
    
    // Check if user has already verified age
    const ageVerified = localStorage.getItem('ageVerified');
    
    if (!ageVerified) {
        ageModal.style.display = 'flex';
    } else {
        ageModal.style.display = 'none';
    }
    
    confirmBtn.addEventListener('click', function() {
        localStorage.setItem('ageVerified', 'true');
        ageModal.style.display = 'none';
        document.body.style.overflow = 'auto';
    });
    
    rejectBtn.addEventListener('click', function() {
        window.location.href = 'https://www.google.com';
    });
}

// Mobile Menu Toggle
function setupMobileMenu() {
    const mobileToggle = document.querySelector('.mobile-menu-toggle');
    const mainNav = document.querySelector('.main-nav');
    
    if (mobileToggle && mainNav) {
        mobileToggle.addEventListener('click', function() {
            mainNav.classList.toggle('active');
            const icon = mobileToggle.querySelector('i');
            
            if (mainNav.classList.contains('active')) {
                icon.classList.remove('fa-bars');
                icon.classList.add('fa-times');
            } else {
                icon.classList.remove('fa-times');
                icon.classList.add('fa-bars');
            }
        });
        
        // Close menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!mobileToggle.contains(e.target) && !mainNav.contains(e.target)) {
                mainNav.classList.remove('active');
                const icon = mobileToggle.querySelector('i');
                icon.classList.remove('fa-times');
                icon.classList.add('fa-bars');
            }
        });
    }
}

// Search Functionality
function setupSearch() {
    const searchInput = document.querySelector('.search-container input');
    const searchBtn = document.querySelector('.search-btn');
    
    if (searchInput && searchBtn) {
        searchBtn.addEventListener('click', performSearch);
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
        
        // Search suggestions (placeholder)
        searchInput.addEventListener('input', function() {
            const query = this.value.toLowerCase();
            if (query.length > 2) {
                // Here you would implement search suggestions
                console.log('Searching for:', query);
            }
        });
    }
}

function performSearch() {
    const searchInput = document.querySelector('.search-container input');
    const query = searchInput.value.trim();
    
    if (query) {
        console.log('Performing search for:', query);
        // Here you would implement actual search functionality
        // For now, we'll just show an alert
        alert(`Searching for: ${query}`);
    }
}

// Content Cards Interaction
function setupContentCards() {
    const contentCards = document.querySelectorAll('.content-card');
    
    contentCards.forEach(card => {
        const playBtn = card.querySelector('.play-btn');
        
        if (playBtn) {
            playBtn.addEventListener('click', function(e) {
                e.stopPropagation();
                const cardTitle = card.querySelector('h3').textContent;
                openVideoPlayer(cardTitle);
            });
        }
        
        card.addEventListener('click', function() {
            const cardTitle = this.querySelector('h3').textContent;
            showContentDetails(cardTitle);
        });
    });
}

// Scroll Effects
function setupScrollEffects() {
    const header = document.querySelector('header');
    
    window.addEventListener('scroll', function() {
        if (window.scrollY > 100) {
            header.style.background = 'rgba(10, 10, 10, 0.95)';
        } else {
            header.style.background = 'linear-gradient(180deg, rgba(10, 10, 10, 0.9), transparent)';
        }
    });
    
    // Intersection Observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);
    
    // Observe content sections
    const sections = document.querySelectorAll('.content-section, .categories-section, .premium-section');
    sections.forEach(section => {
        section.style.opacity = '0';
        section.style.transform = 'translateY(30px)';
        section.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(section);
    });
}

// Video Player
function setupVideoPlayer() {
    // This would integrate with a video player library like Video.js or Plyr
    console.log('Video player setup initialized');
}

function openVideoPlayer(title) {
    // Create modal for video player
    const modal = document.createElement('div');
    modal.className = 'video-modal';
    modal.innerHTML = `
        <div class="video-modal-content">
            <div class="video-header">
                <h3>${title}</h3>
                <button class="close-video">&times;</button>
            </div>
            <div class="video-container">
                <video controls autoplay>
                    <source src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4" type="video/mp4">
                    Your browser does not support the video tag.
                </video>
            </div>
            <div class="video-info">
                <div class="video-stats">
                    <span><i class="fas fa-eye"></i> 1.2M views</span>
                    <span><i class="fas fa-star"></i> 4.8 rating</span>
                    <span><i class="fas fa-clock"></i> 12:45</span>
                </div>
                <div class="video-actions">
                    <button class="action-btn"><i class="fas fa-thumbs-up"></i> Like</button>
                    <button class="action-btn"><i class="fas fa-heart"></i> Favorite</button>
                    <button class="action-btn"><i class="fas fa-share"></i> Share</button>
                    <button class="action-btn"><i class="fas fa-download"></i> Download</button>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    document.body.style.overflow = 'hidden';
    
    // Close modal functionality
    const closeBtn = modal.querySelector('.close-video');
    closeBtn.addEventListener('click', function() {
        document.body.removeChild(modal);
        document.body.style.overflow = 'auto';
    });
    
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            document.body.removeChild(modal);
            document.body.style.overflow = 'auto';
        }
    });
}

// Modal System
function setupModals() {
    // Login Modal
    const loginBtn = document.querySelector('.login-btn');
    if (loginBtn) {
        loginBtn.addEventListener('click', function(e) {
            e.preventDefault();
            openLoginModal();
        });
    }
    
    // Signup Modal
    const signupBtn = document.querySelector('.signup-btn');
    if (signupBtn) {
        signupBtn.addEventListener('click', function(e) {
            e.preventDefault();
            openSignupModal();
        });
    }
}

function openLoginModal() {
    const modal = createModal('Login to LustFlix', `
        <form class="auth-form">
            <div class="form-group">
                <label for="email">Email</label>
                <input type="email" id="email" required>
            </div>
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" required>
            </div>
            <div class="form-options">
                <label class="checkbox">
                    <input type="checkbox"> Remember me
                </label>
                <a href="#" class="forgot-password">Forgot password?</a>
            </div>
            <button type="submit" class="primary-btn">Sign In</button>
            <p class="auth-switch">Don't have an account? <a href="#" onclick="openSignupModal()">Sign up</a></p>
        </form>
    `);
}

function openSignupModal() {
    const modal = createModal('Join LustFlix', `
        <form class="auth-form" id="signupForm">
            <div class="form-group">
                <label for="username">Username</label>
                <input type="text" id="username" required minlength="3" maxlength="20">
                <span class="input-hint">3-20 characters, letters and numbers only</span>
            </div>
            <div class="form-group">
                <label for="email">Email</label>
                <input type="email" id="email" required>
                <span class="input-hint">We'll send a verification email</span>
            </div>
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" required minlength="8">
                <span class="input-hint">At least 8 characters with numbers and symbols</span>
            </div>
            <div class="form-group">
                <label for="confirmPassword">Confirm Password</label>
                <input type="password" id="confirmPassword" required>
                <span class="input-hint">Must match your password</span>
            </div>

            <!-- Human Verification Section -->
            <div class="human-verification">
                <h3>Human Verification</h3>
                <div class="captcha-container">
                    <div class="math-captcha">
                        <label>Solve this simple math problem:</label>
                        <div class="math-problem" id="mathProblem">
                            <span id="num1"></span> + <span id="num2"></span> =
                            <input type="number" id="mathAnswer" required placeholder="?">
                        </div>
                        <button type="button" class="refresh-captcha" onclick="generateMathCaptcha()">
                            <i class="fas fa-refresh"></i> New Problem
                        </button>
                    </div>

                    <div class="image-captcha">
                        <label>Select all images with adult content warnings:</label>
                        <div class="captcha-images" id="imageCaptcha">
                            <!-- Images will be generated dynamically -->
                        </div>
                        <button type="button" class="refresh-captcha" onclick="generateImageCaptcha()">
                            <i class="fas fa-refresh"></i> New Images
                        </button>
                    </div>

                    <div class="checkbox-captcha">
                        <label class="captcha-checkbox">
                            <input type="checkbox" id="humanCheck" required>
                            <span class="checkmark"></span>
                            I'm not a robot
                        </label>
                    </div>
                </div>
            </div>

            <div class="form-options">
                <label class="checkbox">
                    <input type="checkbox" id="termsCheck" required> I agree to the <a href="#" target="_blank">Terms of Service</a> and <a href="#" target="_blank">Privacy Policy</a>
                </label>
                <label class="checkbox">
                    <input type="checkbox" id="ageCheck" required> I confirm that I am 18 years or older and legally allowed to view adult content
                </label>
                <label class="checkbox">
                    <input type="checkbox" id="marketingCheck"> I want to receive updates and promotional emails (optional)
                </label>
            </div>

            <button type="submit" class="primary-btn" id="createAccountBtn" disabled>Create Account</button>
            <p class="auth-switch">Already have an account? <a href="#" onclick="openLoginModal()">Sign in</a></p>
        </form>
    `);

    // Initialize captcha and form validation
    setTimeout(() => {
        generateMathCaptcha();
        generateImageCaptcha();
        setupSignupValidation();
    }, 100);
}

function createModal(title, content) {
    const modal = document.createElement('div');
    modal.className = 'auth-modal';
    modal.innerHTML = `
        <div class="auth-modal-content">
            <div class="modal-header">
                <h2>${title}</h2>
                <button class="close-modal">&times;</button>
            </div>
            <div class="modal-body">
                ${content}
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    document.body.style.overflow = 'hidden';
    
    // Close modal functionality
    const closeBtn = modal.querySelector('.close-modal');
    closeBtn.addEventListener('click', function() {
        document.body.removeChild(modal);
        document.body.style.overflow = 'auto';
    });
    
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            document.body.removeChild(modal);
            document.body.style.overflow = 'auto';
        }
    });
    
    return modal;
}

// Generate Dynamic Content
function generateDynamicContent() {
    generateContentCards();
    generateCategories();
}

function generateContentCards() {
    const carousel = document.querySelector('.content-carousel');
    if (!carousel) return;
    
    // Clear existing placeholder
    carousel.innerHTML = '';
    
    const sampleContent = [
        { title: 'Premium HD Content', views: '2.1M', rating: '4.9', duration: '15:32' },
        { title: 'Exclusive Series Episode 1', views: '1.8M', rating: '4.7', duration: '22:15' },
        { title: 'Behind the Scenes', views: '950K', rating: '4.6', duration: '8:45' },
        { title: 'Live Stream Highlights', views: '3.2M', rating: '4.8', duration: '18:20' },
        { title: 'Director\'s Cut', views: '1.5M', rating: '4.9', duration: '25:10' },
        { title: 'Interactive Experience', views: '2.7M', rating: '4.8', duration: '12:30' }
    ];
    
    sampleContent.forEach(content => {
        const card = createContentCard(content);
        carousel.appendChild(card);
    });
    
    // Re-setup event listeners for new cards
    setupContentCards();
}

function createContentCard(content) {
    const card = document.createElement('div');
    card.className = 'content-card';
    card.innerHTML = `
        <div class="card-image">
            <img src="https://via.placeholder.com/300x169/e91e63/ffffff?text=18+" alt="${content.title}">
            <span class="duration">${content.duration}</span>
            <div class="card-overlay">
                <button class="play-btn"><i class="fas fa-play"></i></button>
            </div>
        </div>
        <div class="card-info">
            <h3>${content.title}</h3>
            <div class="meta-info">
                <span class="views"><i class="fas fa-eye"></i> ${content.views}</span>
                <span class="rating"><i class="fas fa-star"></i> ${content.rating}</span>
                <span class="quality">HD</span>
            </div>
        </div>
    `;
    return card;
}

function generateCategories() {
    const categoriesGrid = document.querySelector('.categories-grid');
    if (!categoriesGrid) return;
    
    // Clear existing placeholder
    categoriesGrid.innerHTML = '';
    
    const categories = [
        'Amateur', 'Professional', 'Couples', 'Solo', 'Interactive', 'VR Content',
        'Live Streams', 'Premium', 'Trending', 'New Releases', 'Popular', 'Exclusive'
    ];
    
    categories.forEach(category => {
        const card = document.createElement('div');
        card.className = 'category-card';
        card.innerHTML = `
            <img src="https://via.placeholder.com/200x120/e91e63/ffffff?text=${encodeURIComponent(category)}" alt="${category}">
            <h3>${category}</h3>
        `;
        categoriesGrid.appendChild(card);
    });
}

function showContentDetails(title) {
    console.log('Showing details for:', title);
    // Here you would implement content details page/modal
}

// Utility Functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Captcha and Validation Functions
function generateMathCaptcha() {
    const num1 = Math.floor(Math.random() * 20) + 1;
    const num2 = Math.floor(Math.random() * 20) + 1;

    document.getElementById('num1').textContent = num1;
    document.getElementById('num2').textContent = num2;
    document.getElementById('mathAnswer').value = '';

    // Store correct answer for validation
    window.correctMathAnswer = num1 + num2;
}

function generateImageCaptcha() {
    const imageCaptcha = document.getElementById('imageCaptcha');
    if (!imageCaptcha) return;

    const images = [
        { src: 'https://via.placeholder.com/80x80/ff6b6b/ffffff?text=18+', isAdult: true, id: 1 },
        { src: 'https://via.placeholder.com/80x80/4ecdc4/ffffff?text=SFW', isAdult: false, id: 2 },
        { src: 'https://via.placeholder.com/80x80/45b7d1/ffffff?text=SAFE', isAdult: false, id: 3 },
        { src: 'https://via.placeholder.com/80x80/f9ca24/ffffff?text=WARN', isAdult: true, id: 4 },
        { src: 'https://via.placeholder.com/80x80/6c5ce7/ffffff?text=OK', isAdult: false, id: 5 },
        { src: 'https://via.placeholder.com/80x80/e17055/ffffff?text=18+', isAdult: true, id: 6 }
    ];

    // Shuffle images
    const shuffledImages = images.sort(() => Math.random() - 0.5);

    imageCaptcha.innerHTML = '';
    window.correctImageIds = [];

    shuffledImages.forEach(image => {
        const imageDiv = document.createElement('div');
        imageDiv.className = 'captcha-image';
        imageDiv.innerHTML = `
            <img src="${image.src}" alt="Captcha Image" data-id="${image.id}">
            <input type="checkbox" class="image-checkbox" data-id="${image.id}">
        `;

        if (image.isAdult) {
            window.correctImageIds.push(image.id);
        }

        imageCaptcha.appendChild(imageDiv);
    });
}

function setupSignupValidation() {
    const form = document.getElementById('signupForm');
    const createBtn = document.getElementById('createAccountBtn');
    const inputs = form.querySelectorAll('input[required]');

    // Real-time validation
    inputs.forEach(input => {
        input.addEventListener('input', validateSignupForm);
        input.addEventListener('change', validateSignupForm);
    });

    // Form submission
    form.addEventListener('submit', handleSignupSubmit);

    // Initial validation
    validateSignupForm();
}

function validateSignupForm() {
    const username = document.getElementById('username').value;
    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    const mathAnswer = parseInt(document.getElementById('mathAnswer').value);
    const humanCheck = document.getElementById('humanCheck').checked;
    const termsCheck = document.getElementById('termsCheck').checked;
    const ageCheck = document.getElementById('ageCheck').checked;
    const createBtn = document.getElementById('createAccountBtn');

    let isValid = true;
    let errors = [];

    // Username validation
    if (username.length < 3 || username.length > 20) {
        isValid = false;
        errors.push('Username must be 3-20 characters');
    }

    if (!/^[a-zA-Z0-9]+$/.test(username)) {
        isValid = false;
        errors.push('Username can only contain letters and numbers');
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        isValid = false;
        errors.push('Please enter a valid email address');
    }

    // Password validation
    if (password.length < 8) {
        isValid = false;
        errors.push('Password must be at least 8 characters');
    }

    if (!/(?=.*[0-9])(?=.*[!@#$%^&*])/.test(password)) {
        isValid = false;
        errors.push('Password must contain at least one number and one symbol');
    }

    // Confirm password validation
    if (password !== confirmPassword) {
        isValid = false;
        errors.push('Passwords do not match');
    }

    // Math captcha validation
    if (mathAnswer !== window.correctMathAnswer) {
        isValid = false;
        errors.push('Please solve the math problem correctly');
    }

    // Image captcha validation
    const selectedImages = Array.from(document.querySelectorAll('.image-checkbox:checked'))
        .map(cb => parseInt(cb.dataset.id));

    const correctSelection = window.correctImageIds.sort().toString() === selectedImages.sort().toString();
    if (!correctSelection) {
        isValid = false;
        errors.push('Please select all images with adult content warnings');
    }

    // Checkbox validations
    if (!humanCheck) {
        isValid = false;
        errors.push('Please confirm you are not a robot');
    }

    if (!termsCheck) {
        isValid = false;
        errors.push('Please agree to the Terms of Service and Privacy Policy');
    }

    if (!ageCheck) {
        isValid = false;
        errors.push('You must be 18 or older to create an account');
    }

    // Update button state
    createBtn.disabled = !isValid;
    createBtn.textContent = isValid ? 'Create Account' : 'Please complete all fields';

    // Show/hide errors
    updateValidationErrors(errors);

    return isValid;
}

function updateValidationErrors(errors) {
    let errorContainer = document.querySelector('.validation-errors');

    if (errors.length > 0) {
        if (!errorContainer) {
            errorContainer = document.createElement('div');
            errorContainer.className = 'validation-errors';
            const form = document.getElementById('signupForm');
            form.insertBefore(errorContainer, form.querySelector('.form-options'));
        }

        errorContainer.innerHTML = `
            <h4>Please fix the following issues:</h4>
            <ul>
                ${errors.map(error => `<li>${error}</li>`).join('')}
            </ul>
        `;
    } else if (errorContainer) {
        errorContainer.remove();
    }
}

function handleSignupSubmit(e) {
    e.preventDefault();

    if (!validateSignupForm()) {
        return;
    }

    const createBtn = document.getElementById('createAccountBtn');
    const originalText = createBtn.textContent;

    // Show loading state
    createBtn.disabled = true;
    createBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating Account...';

    // Simulate account creation process
    setTimeout(() => {
        // In a real application, you would send the data to your server
        const formData = {
            username: document.getElementById('username').value,
            email: document.getElementById('email').value,
            password: document.getElementById('password').value,
            marketing: document.getElementById('marketingCheck').checked
        };

        console.log('Account creation data:', formData);

        // Show success message
        showSignupSuccess();

    }, 2000);
}

function showSignupSuccess() {
    const modal = document.querySelector('.auth-modal');
    const modalBody = modal.querySelector('.modal-body');

    modalBody.innerHTML = `
        <div class="signup-success">
            <div class="success-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <h3>Account Created Successfully!</h3>
            <p>Welcome to LustFlix! We've sent a verification email to your address.</p>
            <p>Please check your email and click the verification link to activate your account.</p>
            <div class="success-actions">
                <button class="primary-btn" onclick="closeModal()">Continue</button>
                <button class="secondary-btn" onclick="openLoginModal()">Sign In Now</button>
            </div>
        </div>
    `;
}

function closeModal() {
    const modal = document.querySelector('.auth-modal') || document.querySelector('.video-modal');
    if (modal) {
        document.body.removeChild(modal);
        document.body.style.overflow = 'auto';
    }
}

// Export functions for global access
window.openLoginModal = openLoginModal;
window.openSignupModal = openSignupModal;
window.generateMathCaptcha = generateMathCaptcha;
window.generateImageCaptcha = generateImageCaptcha;
window.closeModal = closeModal;
