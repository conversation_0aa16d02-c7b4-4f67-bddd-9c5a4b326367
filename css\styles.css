/* LustFlix - Main Styles */

/* CSS Variables for Design System */
:root {
    /* Primary Colors */
    --primary-pink: #e91e63;
    --primary-pink-dark: #c2185b;
    --primary-pink-light: #f48fb1;
    --accent-pink: #ff4081;
    
    /* Background Colors */
    --bg-primary: #0a0a0a;
    --bg-secondary: #1a1a1a;
    --bg-tertiary: #2a2a2a;
    --bg-card: #1e1e1e;
    --bg-modal: rgba(0, 0, 0, 0.9);
    
    /* Text Colors */
    --text-primary: #ffffff;
    --text-secondary: #b3b3b3;
    --text-muted: #666666;
    
    /* Border Colors */
    --border-primary: #333333;
    --border-secondary: #444444;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--primary-pink), var(--accent-pink));
    --gradient-dark: linear-gradient(180deg, transparent, rgba(0, 0, 0, 0.8));
    
    /* Shadows */
    --shadow-card: 0 4px 20px rgba(233, 30, 99, 0.1);
    --shadow-hover: 0 8px 30px rgba(233, 30, 99, 0.2);
    
    /* Typography */
    --font-primary: 'Roboto', sans-serif;
    --font-logo: 'Bebas Neue', cursive;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-xxl: 3rem;
    
    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    
    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    --transition-bounce: 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --transition-smooth: 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);

    /* Animation Keyframes */
    --pulse-animation: pulse 2s infinite;
    --float-animation: float 3s ease-in-out infinite;
    --glow-animation: glow 2s ease-in-out infinite alternate;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-primary);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Header Styles */
header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: linear-gradient(180deg, rgba(10, 10, 10, 0.9), transparent);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: var(--spacing-md) 0;
    transition: var(--transition-normal);
}

.header-container {
    max-width: 1400px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--spacing-lg);
}

.logo h1 {
    font-family: var(--font-logo);
    font-size: 2.5rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: 2px;
    text-decoration: none;
}

.logo a {
    text-decoration: none;
}

.main-nav {
    display: flex;
    list-style: none;
    gap: var(--spacing-xl);
}

.main-nav a {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition-smooth);
    position: relative;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-sm);
}

.main-nav a::before {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    border-radius: 2px;
    transition: var(--transition-smooth);
    transform: translateX(-50%);
}

.main-nav a:hover,
.main-nav a.active {
    color: var(--text-primary);
    background: rgba(233, 30, 99, 0.1);
    transform: translateY(-2px);
}

.main-nav a:hover::before,
.main-nav a.active::before {
    width: 100%;
}

.main-nav a.active::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--gradient-primary);
    border-radius: 2px;
    animation: slideInFromLeft 0.5s ease;
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.search-container {
    position: relative;
    display: flex;
    align-items: center;
}

.search-container input {
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-primary);
    width: 300px;
    transition: var(--transition-smooth);
    position: relative;
}

.search-container input:focus {
    outline: none;
    border-color: var(--primary-pink);
    box-shadow: 0 0 0 3px rgba(233, 30, 99, 0.2);
    transform: scale(1.02);
    background: var(--bg-tertiary);
}

.search-container input:hover {
    border-color: var(--border-secondary);
    transform: translateY(-1px);
}

.search-btn {
    position: absolute;
    right: var(--spacing-sm);
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-sm);
    transition: var(--transition-bounce);
    border-radius: 50%;
}

.search-btn:hover {
    color: var(--primary-pink);
    background: rgba(233, 30, 99, 0.1);
    transform: scale(1.2) rotate(15deg);
}

.search-btn:active {
    transform: scale(1.1) rotate(0deg);
    transition: var(--transition-fast);
}

.user-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.upload-btn,
.login-btn,
.signup-btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-smooth);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    position: relative;
    overflow: hidden;
}

.upload-btn {
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    color: var(--text-primary);
}

.upload-btn:hover {
    background: var(--bg-tertiary);
    border-color: var(--primary-pink);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(233, 30, 99, 0.2);
}

.upload-btn:active {
    transform: translateY(0);
    transition: var(--transition-fast);
}

.login-btn {
    background: transparent;
    border: 1px solid var(--border-secondary);
    color: var(--text-primary);
}

.login-btn:hover {
    border-color: var(--primary-pink);
    color: var(--primary-pink);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(233, 30, 99, 0.1);
}

.login-btn:active {
    transform: translateY(0);
    transition: var(--transition-fast);
}

.signup-btn {
    background: var(--gradient-primary);
    border: none;
    color: white;
    animation: var(--pulse-animation);
}

.signup-btn:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-hover);
    animation: none;
}

.signup-btn:active {
    transform: translateY(-1px);
    transition: var(--transition-fast);
}

.mobile-menu-toggle {
    display: none;
    color: var(--text-primary);
    font-size: 1.5rem;
    cursor: pointer;
}

/* Hero Section */
.hero {
    height: 100vh;
    background: linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.6)),
                url('https://via.placeholder.com/1920x1080?text=Hero+Background') center/cover;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    position: relative;
}

.hero-content h2 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-content p {
    font-size: 1.25rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xxl);
    max-width: 600px;
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-lg);
    justify-content: center;
}

.primary-btn,
.secondary-btn {
    padding: var(--spacing-lg) var(--spacing-xxl);
    border-radius: var(--radius-md);
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-bounce);
    text-decoration: none;
    display: inline-block;
    position: relative;
    overflow: hidden;
}

.primary-btn::before,
.secondary-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-slow);
}

.primary-btn:hover::before,
.secondary-btn:hover::before {
    left: 100%;
}

.primary-btn {
    background: var(--gradient-primary);
    border: none;
    color: white;
    animation: var(--glow-animation);
}

.primary-btn:hover {
    transform: translateY(-4px) scale(1.05);
    box-shadow: var(--shadow-hover);
    animation: none;
}

.primary-btn:active {
    transform: translateY(-2px) scale(1.02);
    transition: var(--transition-fast);
}

.secondary-btn {
    background: transparent;
    border: 2px solid var(--primary-pink);
    color: var(--primary-pink);
}

.secondary-btn:hover {
    background: var(--primary-pink);
    color: white;
    transform: translateY(-4px) scale(1.05);
    box-shadow: 0 8px 25px rgba(233, 30, 99, 0.3);
}

.secondary-btn:active {
    transform: translateY(-2px) scale(1.02);
    transition: var(--transition-fast);
}

/* Content Sections */
.content-section,
.categories-section {
    padding: var(--spacing-xxl) var(--spacing-lg);
    max-width: 1400px;
    margin: 0 auto;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
}

.section-header h2 {
    font-size: 2rem;
    font-weight: 600;
}

.see-all {
    color: var(--primary-pink);
    text-decoration: none;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    transition: var(--transition-fast);
}

.see-all:hover {
    color: var(--primary-pink-light);
}

/* Content Carousel */
.content-carousel {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--spacing-lg);
}

.content-card {
    background: var(--bg-card);
    border-radius: var(--radius-lg);
    overflow: hidden;
    transition: var(--transition-smooth);
    cursor: pointer;
    position: relative;
}

.content-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(233, 30, 99, 0.1), transparent);
    opacity: 0;
    transition: var(--transition-normal);
    z-index: 1;
}

.content-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-hover);
}

.content-card:hover::before {
    opacity: 1;
}

.card-image {
    position: relative;
    aspect-ratio: 16/9;
    overflow: hidden;
}

.card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-slow);
}

.content-card:hover .card-image img {
    transform: scale(1.05);
}

.duration {
    position: absolute;
    bottom: var(--spacing-sm);
    right: var(--spacing-sm);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.875rem;
    font-weight: 500;
}

.card-overlay {
    position: absolute;
    inset: 0;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition-normal);
}

.content-card:hover .card-overlay {
    opacity: 1;
}

.play-btn {
    background: var(--gradient-primary);
    border: none;
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    font-size: 1.5rem;
    cursor: pointer;
    transition: var(--transition-bounce);
    position: relative;
    animation: var(--pulse-animation);
}

.play-btn::before {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border-radius: 50%;
    background: var(--gradient-primary);
    opacity: 0.3;
    z-index: -1;
    transition: var(--transition-normal);
}

.play-btn:hover {
    transform: scale(1.2);
    animation: none;
    box-shadow: 0 0 30px rgba(233, 30, 99, 0.6);
}

.play-btn:hover::before {
    opacity: 0.6;
    transform: scale(1.2);
}

.play-btn:active {
    transform: scale(1.1);
    transition: var(--transition-fast);
}

.card-info {
    padding: var(--spacing-lg);
}

.card-info h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.meta-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.meta-info span {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.quality {
    background: var(--primary-pink);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-weight: 600;
    font-size: 0.75rem;
}

/* Categories Grid */
.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: var(--spacing-lg);
}

.category-card {
    background: var(--bg-card);
    border-radius: var(--radius-lg);
    overflow: hidden;
    text-align: center;
    transition: var(--transition-smooth);
    cursor: pointer;
    position: relative;
}

.category-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: var(--transition-normal);
    z-index: 1;
}

.category-card:hover {
    transform: translateY(-6px) rotateY(5deg);
    box-shadow: var(--shadow-hover);
}

.category-card:hover::after {
    opacity: 0.1;
}

.category-card h3 {
    position: relative;
    z-index: 2;
}

.category-card img {
    width: 100%;
    height: 120px;
    object-fit: cover;
}

.category-card h3 {
    padding: var(--spacing-lg);
    font-size: 1.1rem;
    font-weight: 600;
}

/* Premium Section */
.premium-section {
    background: var(--bg-secondary);
    padding: var(--spacing-xxl) var(--spacing-lg);
    margin: var(--spacing-xxl) 0;
}

.premium-content {
    max-width: 1200px;
    margin: 0 auto;
    text-align: center;
}

.premium-content h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.premium-content > p {
    font-size: 1.25rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xxl);
}

.premium-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xxl);
}

.feature {
    text-align: center;
}

.feature i {
    font-size: 3rem;
    color: var(--primary-pink);
    margin-bottom: var(--spacing-lg);
}

.feature h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
}

.feature p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Footer */
footer {
    background: var(--bg-secondary);
    padding: var(--spacing-xxl) var(--spacing-lg) var(--spacing-lg);
    border-top: 1px solid var(--border-primary);
}

.footer-container {
    max-width: 1400px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr 2fr 1fr;
    gap: var(--spacing-xxl);
    margin-bottom: var(--spacing-xl);
}

.footer-logo h2 {
    font-family: var(--font-logo);
    font-size: 2rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--spacing-sm);
}

.footer-logo p {
    color: var(--text-secondary);
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-xl);
}

.link-group h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: var(--spacing-lg);
    color: var(--primary-pink);
}

.link-group ul {
    list-style: none;
}

.link-group li {
    margin-bottom: var(--spacing-sm);
}

.link-group a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition-fast);
}

.link-group a:hover {
    color: var(--primary-pink);
}

.footer-social h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: var(--spacing-lg);
    color: var(--primary-pink);
}

.social-icons {
    display: flex;
    gap: var(--spacing-md);
}

.social-icons a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    border-radius: 50%;
    text-decoration: none;
    transition: var(--transition-fast);
}

.social-icons a:hover {
    background: var(--primary-pink);
    color: white;
    transform: translateY(-2px);
}

.footer-bottom {
    border-top: 1px solid var(--border-primary);
    padding-top: var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: var(--text-muted);
    font-size: 0.875rem;
}

.age-verification {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.age-verification img {
    width: 30px;
    height: 30px;
}

/* Age Verification Modal */
.age-modal {
    position: fixed;
    inset: 0;
    background: var(--bg-modal);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    backdrop-filter: blur(10px);
}

.age-modal-content {
    background: var(--bg-card);
    padding: var(--spacing-xxl);
    border-radius: var(--radius-xl);
    text-align: center;
    max-width: 500px;
    margin: var(--spacing-lg);
    box-shadow: var(--shadow-hover);
}

.age-modal-content h2 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
    color: var(--primary-pink);
}

.age-modal-content p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    line-height: 1.6;
}

.age-buttons {
    display: flex;
    gap: var(--spacing-lg);
    justify-content: center;
    margin-bottom: var(--spacing-lg);
}

.disclaimer {
    font-size: 0.875rem;
    color: var(--text-muted);
    line-height: 1.5;
}

/* Authentication Modal Styles */
.auth-modal {
    position: fixed;
    inset: 0;
    background: var(--bg-modal);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    backdrop-filter: blur(10px);
}

.auth-modal-content {
    background: var(--bg-card);
    border-radius: var(--radius-xl);
    max-width: 450px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: var(--shadow-hover);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xl) var(--spacing-xl) 0;
    border-bottom: 1px solid var(--border-primary);
    margin-bottom: var(--spacing-xl);
}

.modal-header h2 {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--primary-pink);
}

.close-modal {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 2rem;
    cursor: pointer;
    padding: 0;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: var(--transition-fast);
}

.close-modal:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.modal-body {
    padding: 0 var(--spacing-xl) var(--spacing-xl);
}

.auth-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.form-group label {
    font-weight: 500;
    color: var(--text-primary);
}

.form-group input {
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    color: var(--text-primary);
    font-size: 1rem;
    transition: var(--transition-fast);
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-pink);
    box-shadow: 0 0 0 2px rgba(233, 30, 99, 0.2);
}

.form-options {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.checkbox {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 0.875rem;
    color: var(--text-secondary);
    cursor: pointer;
}

.checkbox input {
    margin: 0;
    width: auto;
}

.forgot-password {
    color: var(--primary-pink);
    text-decoration: none;
    font-size: 0.875rem;
    transition: var(--transition-fast);
}

.forgot-password:hover {
    color: var(--primary-pink-light);
}

.auth-switch {
    text-align: center;
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.auth-switch a {
    color: var(--primary-pink);
    text-decoration: none;
    transition: var(--transition-fast);
}

.auth-switch a:hover {
    color: var(--primary-pink-light);
}

/* Video Modal Styles */
.video-modal {
    position: fixed;
    inset: 0;
    background: var(--bg-modal);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    backdrop-filter: blur(10px);
}

.video-modal-content {
    background: var(--bg-card);
    border-radius: var(--radius-xl);
    max-width: 1200px;
    width: 95%;
    max-height: 95vh;
    overflow: hidden;
    box-shadow: var(--shadow-hover);
}

.video-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg) var(--spacing-xl);
    border-bottom: 1px solid var(--border-primary);
}

.video-header h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.close-video {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 2rem;
    cursor: pointer;
    padding: 0;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: var(--transition-fast);
}

.close-video:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.video-container {
    position: relative;
    background: black;
}

.video-container video {
    width: 100%;
    height: auto;
    max-height: 70vh;
}

.video-info {
    padding: var(--spacing-xl);
}

.video-stats {
    display: flex;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-lg);
    color: var(--text-secondary);
}

.video-stats span {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.video-actions {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.action-btn {
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    color: var(--text-primary);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 0.875rem;
}

.action-btn:hover {
    background: var(--bg-tertiary);
    border-color: var(--primary-pink);
    color: var(--primary-pink);
    transform: translateY(-2px);
}

/* Human Verification Styles */
.human-verification {
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin: var(--spacing-lg) 0;
}

.human-verification h3 {
    color: var(--primary-pink);
    margin-bottom: var(--spacing-lg);
    font-size: 1.25rem;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.human-verification h3::before {
    content: '🛡️';
    font-size: 1.5rem;
}

.captcha-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

/* Math Captcha */
.math-captcha {
    background: var(--bg-tertiary);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
}

.math-captcha label {
    display: block;
    margin-bottom: var(--spacing-md);
    font-weight: 500;
}

.math-problem {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
}

.math-problem span {
    color: var(--primary-pink);
    font-size: 1.5rem;
}

.math-problem input {
    width: 80px;
    text-align: center;
    background: var(--bg-primary);
    border: 2px solid var(--border-primary);
    border-radius: var(--radius-sm);
    padding: var(--spacing-sm);
    color: var(--text-primary);
    font-size: 1.25rem;
    font-weight: 600;
}

.math-problem input:focus {
    border-color: var(--primary-pink);
    box-shadow: 0 0 0 2px rgba(233, 30, 99, 0.2);
}

/* Image Captcha */
.image-captcha {
    background: var(--bg-tertiary);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
}

.image-captcha label {
    display: block;
    margin-bottom: var(--spacing-md);
    font-weight: 500;
}

.captcha-images {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.captcha-image {
    position: relative;
    border-radius: var(--radius-md);
    overflow: hidden;
    cursor: pointer;
    transition: var(--transition-normal);
}

.captcha-image:hover {
    transform: scale(1.05);
}

.captcha-image img {
    width: 100%;
    height: 80px;
    object-fit: cover;
    display: block;
}

.captcha-image .image-checkbox {
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    width: 20px;
    height: 20px;
    cursor: pointer;
}

.captcha-image .image-checkbox:checked + img {
    border: 3px solid var(--primary-pink);
}

/* Checkbox Captcha */
.checkbox-captcha {
    background: var(--bg-tertiary);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    text-align: center;
}

.captcha-checkbox {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-md);
    font-size: 1.1rem;
    font-weight: 500;
    cursor: pointer;
    padding: var(--spacing-md) var(--spacing-lg);
    border: 2px solid var(--border-primary);
    border-radius: var(--radius-md);
    transition: var(--transition-normal);
    background: var(--bg-primary);
}

.captcha-checkbox:hover {
    border-color: var(--primary-pink);
    background: var(--bg-secondary);
}

.captcha-checkbox input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 24px;
    height: 24px;
    border: 2px solid var(--border-secondary);
    border-radius: var(--radius-sm);
    position: relative;
    transition: var(--transition-normal);
}

.captcha-checkbox input[type="checkbox"]:checked + .checkmark {
    background: var(--gradient-primary);
    border-color: var(--primary-pink);
}

.captcha-checkbox input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    font-size: 14px;
}

/* Refresh Captcha Button */
.refresh-captcha {
    background: var(--bg-primary);
    border: 1px solid var(--border-primary);
    color: var(--text-secondary);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: var(--transition-normal);
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.refresh-captcha:hover {
    background: var(--bg-secondary);
    border-color: var(--primary-pink);
    color: var(--primary-pink);
    transform: translateY(-1px);
}

.refresh-captcha i {
    transition: var(--transition-normal);
}

.refresh-captcha:hover i {
    transform: rotate(180deg);
}

/* Input Hints */
.input-hint {
    font-size: 0.75rem;
    color: var(--text-muted);
    margin-top: var(--spacing-xs);
    display: block;
}

/* Validation Errors */
.validation-errors {
    background: rgba(231, 76, 60, 0.1);
    border: 1px solid #e74c3c;
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    margin: var(--spacing-md) 0;
}

.validation-errors h4 {
    color: #e74c3c;
    margin-bottom: var(--spacing-sm);
    font-size: 1rem;
}

.validation-errors ul {
    margin: 0;
    padding-left: var(--spacing-lg);
}

.validation-errors li {
    color: #e74c3c;
    font-size: 0.875rem;
    margin-bottom: var(--spacing-xs);
}

/* Signup Success */
.signup-success {
    text-align: center;
    padding: var(--spacing-xl);
}

.success-icon {
    font-size: 4rem;
    color: #27ae60;
    margin-bottom: var(--spacing-lg);
    animation: bounce 1s ease;
}

.signup-success h3 {
    color: var(--primary-pink);
    margin-bottom: var(--spacing-md);
    font-size: 1.5rem;
}

.signup-success p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
    line-height: 1.6;
}

.success-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    margin-top: var(--spacing-xl);
}

/* Button Loading State */
.primary-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.primary-btn:disabled:hover {
    transform: none;
    box-shadow: none;
}

/* Keyframe Animations */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(233, 30, 99, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(233, 30, 99, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(233, 30, 99, 0);
    }
}

@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
    100% {
        transform: translateY(0px);
    }
}

@keyframes glow {
    from {
        box-shadow: 0 0 20px rgba(233, 30, 99, 0.3);
    }
    to {
        box-shadow: 0 0 30px rgba(233, 30, 99, 0.6);
    }
}

@keyframes slideInFromLeft {
    0% {
        transform: translateX(-100%);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInFromRight {
    0% {
        transform: translateX(100%);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInFromTop {
    0% {
        transform: translateY(-100%);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideInFromBottom {
    0% {
        transform: translateY(100%);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes fadeIn {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}

@keyframes fadeInUp {
    0% {
        opacity: 0;
        transform: translateY(30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    0% {
        transform: scale(0.8);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-10px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(10px);
    }
}

@keyframes rotate {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes heartbeat {
    0% {
        transform: scale(1);
    }
    14% {
        transform: scale(1.3);
    }
    28% {
        transform: scale(1);
    }
    42% {
        transform: scale(1.3);
    }
    70% {
        transform: scale(1);
    }
}
