/* LustFlix - Responsive Styles */

/* Tablet Styles */
@media (max-width: 1024px) {
    .header-container {
        padding: 0 var(--spacing-md);
    }
    
    .search-container input {
        width: 250px;
    }
    
    .hero-content h2 {
        font-size: 3rem;
    }
    
    .content-carousel {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
    
    .categories-grid {
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    }
    
    .premium-features {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
    
    .footer-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
        text-align: center;
    }
    
    .footer-links {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .footer-bottom {
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }
}

/* Mobile Styles */
@media (max-width: 768px) {
    /* Header Mobile */
    .header-container {
        padding: 0 var(--spacing-md);
    }
    
    .main-nav {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--bg-secondary);
        flex-direction: column;
        padding: var(--spacing-lg);
        border-top: 1px solid var(--border-primary);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }
    
    .main-nav.active {
        display: flex;
    }
    
    .main-nav li {
        margin-bottom: var(--spacing-md);
    }
    
    .search-container {
        display: none;
    }
    
    .user-actions {
        gap: var(--spacing-sm);
    }
    
    .upload-btn {
        display: none;
    }
    
    .login-btn,
    .signup-btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: 0.875rem;
    }
    
    .mobile-menu-toggle {
        display: block;
    }
    
    /* Hero Mobile */
    .hero {
        height: 80vh;
        padding: 0 var(--spacing-md);
    }
    
    .hero-content h2 {
        font-size: 2.5rem;
        margin-bottom: var(--spacing-md);
    }
    
    .hero-content p {
        font-size: 1.1rem;
        margin-bottom: var(--spacing-xl);
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-md);
    }
    
    .primary-btn,
    .secondary-btn {
        width: 100%;
        max-width: 300px;
        padding: var(--spacing-md) var(--spacing-lg);
    }
    
    /* Content Mobile */
    .content-section,
    .categories-section {
        padding: var(--spacing-xl) var(--spacing-md);
    }
    
    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
        margin-bottom: var(--spacing-lg);
    }
    
    .section-header h2 {
        font-size: 1.75rem;
    }
    
    .content-carousel {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: var(--spacing-md);
    }
    
    .categories-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: var(--spacing-md);
    }
    
    .category-card img {
        height: 100px;
    }
    
    .category-card h3 {
        padding: var(--spacing-md);
        font-size: 1rem;
    }
    
    /* Premium Mobile */
    .premium-section {
        padding: var(--spacing-xl) var(--spacing-md);
    }
    
    .premium-content h2 {
        font-size: 2rem;
    }
    
    .premium-content > p {
        font-size: 1.1rem;
    }
    
    .premium-features {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
        margin-bottom: var(--spacing-xl);
    }
    
    .feature i {
        font-size: 2.5rem;
    }
    
    .feature h3 {
        font-size: 1.25rem;
    }
    
    /* Footer Mobile */
    .footer-links {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
    
    .social-icons {
        justify-content: center;
    }
    
    /* Age Modal Mobile */
    .age-modal-content {
        margin: var(--spacing-md);
        padding: var(--spacing-xl);
    }
    
    .age-modal-content h2 {
        font-size: 1.75rem;
    }
    
    .age-buttons {
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .age-buttons button {
        width: 100%;
    }
}

/* Small Mobile Styles */
@media (max-width: 480px) {
    .logo h1 {
        font-size: 2rem;
    }
    
    .hero-content h2 {
        font-size: 2rem;
    }
    
    .hero-content p {
        font-size: 1rem;
    }
    
    .content-carousel {
        grid-template-columns: 1fr;
    }
    
    .categories-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .premium-content h2 {
        font-size: 1.75rem;
    }
    
    .age-modal-content {
        padding: var(--spacing-lg);
    }
    
    .age-modal-content h2 {
        font-size: 1.5rem;
    }
}

/* Landscape Mobile */
@media (max-width: 768px) and (orientation: landscape) {
    .hero {
        height: 100vh;
    }
    
    .hero-content h2 {
        font-size: 2rem;
    }
    
    .hero-buttons {
        flex-direction: row;
        justify-content: center;
    }
    
    .primary-btn,
    .secondary-btn {
        width: auto;
        min-width: 150px;
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .logo h1 {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
    
    .hero-content h2 {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
}

/* Dark Mode Support (for devices that prefer dark mode) */
@media (prefers-color-scheme: dark) {
    /* Already using dark theme, but can add specific adjustments if needed */
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Print Styles */
@media print {
    .age-modal,
    header,
    .hero,
    .premium-section,
    footer {
        display: none;
    }
    
    body {
        background: white;
        color: black;
    }
    
    .content-section,
    .categories-section {
        padding: var(--spacing-md) 0;
    }
}

/* Focus Styles for Accessibility */
@media (prefers-reduced-motion: no-preference) {
    button:focus,
    input:focus,
    a:focus {
        outline: 2px solid var(--primary-pink);
        outline-offset: 2px;
    }
}

/* Hover Effects Only on Non-Touch Devices */
@media (hover: hover) and (pointer: fine) {
    .content-card:hover {
        transform: translateY(-4px);
    }
    
    .category-card:hover {
        transform: translateY(-4px);
    }
    
    .primary-btn:hover {
        transform: translateY(-2px);
    }
    
    .social-icons a:hover {
        transform: translateY(-2px);
    }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
    .content-card,
    .category-card {
        transition: none;
    }
    
    .play-btn {
        width: 80px;
        height: 80px;
        font-size: 2rem;
    }
    
    button,
    .primary-btn,
    .secondary-btn {
        min-height: 44px;
        min-width: 44px;
    }
}

/* Large Desktop Styles */
@media (min-width: 1400px) {
    .content-carousel {
        grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    }
    
    .categories-grid {
        grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    }
    
    .hero-content h2 {
        font-size: 4rem;
    }
    
    .premium-content h2 {
        font-size: 3rem;
    }
}

/* Ultra-wide Displays */
@media (min-width: 1920px) {
    .header-container,
    .content-section,
    .categories-section,
    .premium-content,
    .footer-container {
        max-width: 1600px;
    }
    
    .content-carousel {
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    }
}
